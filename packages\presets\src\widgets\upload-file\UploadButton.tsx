import React, { useRef } from "react";

import { PresetsCommand } from "@/command";
import { useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const UploadButton: React.FC = () => {
  const runner = useCommandRunner();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const files = e.target.files;
    runner(PresetsCommand.UploadFiles, { files });
    runner(PresetsCommand.OpenUploadFileList);
    // 清空 input
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.value = "";
      }
    });
  };

  return (
    <div className="pts:relative pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:w-8 pts:h-8">
      <Icon icon="Attachment" />
      <input
        type="file"
        onChange={handleChange}
        ref={inputRef}
        multiple={true}
        className="pts:top-0 pts:left-0 pts:absolute pts:opacity-0 pts:w-8 pts:h-8 pts:cursor-pointer"
      />
    </div>
  );
};

export default UploadButton;
