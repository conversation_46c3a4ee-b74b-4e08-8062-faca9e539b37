import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";

// interface SenderHeaderProps {}

const SenderHeaderInside: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  const widgets = useMemo(() => {
    const widgets = agentConfig?.sender?.slots?.header?.widgets ?? [];
    return widgets.filter((i) => i.placement === "inside" || i.placement === undefined);
  }, [agentConfig]);

  return (
    <div className="ag:flex ag:flex-wrap ag:pb-2">
      {widgets.map((Widget, index) => (
        <div className="ag:w-full" key={index}>
          <Widget.component {...Widget.props} />
        </div>
      ))}
    </div>
  );
};

export default SenderHeaderInside;
