import { nanoid } from "nanoid";
import React, { useCallback, useEffect, useRef, useState } from "react";

import { PresetsCommand } from "@/command";
import { BuildInCommand, StandardResponse, post, useCommandRunner, useSubscribeCommand } from "@cscs-agent/core";

interface FileItem {
  id: string;
  fileId?: string;
  name: string;
  type: "word" | "markdown" | "text" | "json";
  size: string;
  status: "success" | "failed" | "uploading";
  progress?: number; // Upload progress percentage (0-100)
}

enum FileUploadStatus {
  UPLOADING = "uploading",
  PARSING = "parsing",
  READY = "ready",
  ERROR = "error",
}

interface FileUploadResponse {
  id: string;
  file_name: string;
  file_size: number;
  status: FileUploadStatus;
  message: string;
}

const fileTypeIcons = {
  word: { icon: "W", bgColor: "bg-blue-500", textColor: "text-white" },
  markdown: { icon: "</>", bgColor: "bg-cyan-500", textColor: "text-white" },
  text: { icon: "≡", bgColor: "bg-gray-500", textColor: "text-white" },
  json: { icon: "</>", bgColor: "bg-cyan-500", textColor: "text-white" },
};

const FileList: React.FC = () => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const innerRef = useRef<HTMLDivElement | null>(null);
  const wrapperRef = useRef<HTMLDivElement | null>(null);
  const [posX, setPosX] = useState(0);
  const [open, setOpen] = useState(false);
  const runner = useCommandRunner();

  useSubscribeCommand(PresetsCommand.UploadFiles, ({ files }) => {
    uploadFiles(files);
  });

  useSubscribeCommand(PresetsCommand.OpenUploadFileList, () => {
    setOpen(true);
  });

  useSubscribeCommand(PresetsCommand.CloseUploadFileList, () => {
    setOpen(false);
  });

  // Helper function to determine file type based on file extension
  const getFileType = (fileName: string): FileItem["type"] => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "doc":
      case "docx":
        return "word";
      case "md":
        return "markdown";
      case "json":
        return "json";
      default:
        return "text";
    }
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  // Enhanced onUploadProgress method
  const onUploadProgress = useCallback((localFileId: string) => {
    return (progressEvent: { loaded: number; total?: number }) => {
      const { loaded, total } = progressEvent;

      if (!total) return;

      // Calculate progress percentage
      const progressPercentage = Math.round((loaded / total) * 100);

      // Update file progress in state
      setFiles((prevFiles) =>
        prevFiles.map((fileItem) =>
          fileItem.id === localFileId ? { ...fileItem, progress: progressPercentage } : fileItem,
        ),
      );

      // Handle completion
      if (progressPercentage >= 100) {
        // Small delay to show 100% before changing to success
        setTimeout(() => {
          setFiles((prevFiles) =>
            prevFiles.map((fileItem) =>
              fileItem.id === localFileId ? { ...fileItem, status: "success" as const, progress: undefined } : fileItem,
            ),
          );
        }, 200);
      }
    };
  }, []);

  useEffect(() => {
    const uploadedFiles = files.filter((i) => i.status === "success");
    runner(BuildInCommand.UpdateChatFileIdList, {
      setValue: () => {
        console.log(uploadedFiles);
        return uploadedFiles.map((i) => i.fileId);
      },
    });
  }, [files]);

  const uploadFiles = async (files: File[]) => {
    const newFiles = [];
    for (const file of files) {
      // Generate unique ID for this file
      const localFileId = nanoid();

      // Create FileItem for this upload
      const newFileItem: FileItem = {
        id: localFileId,
        name: file.name,
        type: getFileType(file.name),
        size: formatFileSize(file.size),
        status: "uploading",
        progress: 0,
      };

      newFiles.push(newFileItem);

      // Prepare form data
      const formData = new FormData();
      formData.append("file", file);

      // Start upload with progress tracking
      post<StandardResponse<FileUploadResponse>>("/rag/files/upload", formData, {
        onUploadProgress: onUploadProgress(localFileId),
        headers: {
          "Content-Type": "multipart/form-data",
        },
        timeout: 60000, // 60 second timeout for file uploads
      })
        .then((res) => {
          if (res.data.code === 200) {
            const fileId = res.data.data.id;
            setTimeout(() => {
              setFiles((prevFiles) =>
                prevFiles.map((fileItem) =>
                  fileItem.id === localFileId
                    ? { ...fileItem, fileId, status: "success" as const, progress: undefined }
                    : fileItem,
                ),
              );
            }, 200);
          }
        })
        .catch((error) => {
          // Handle upload error
          console.error("Upload failed:", error);
          setFiles((prevFiles) =>
            prevFiles.map((fileItem) =>
              fileItem.id === localFileId ? { ...fileItem, status: "failed" as const, progress: undefined } : fileItem,
            ),
          );
        });
    }
    setFiles(newFiles);
  };

  const removeFile = (id: string) => {
    setFiles(files.filter((file) => file.id !== id));
  };

  const retryUpload = (id: string) => {};

  const swipeList = (direction: "left" | "right") => {
    const step = 204;
    const innerEle = innerRef.current;
    const wrapperEle = innerRef.current;
    const innerWidth = innerEle?.scrollWidth ?? 0;
    const wrapperWidth = wrapperEle?.getBoundingClientRect().width ?? 0;
    let newPosX = direction === "left" ? posX + step : posX - step;

    if (newPosX < wrapperWidth - innerWidth) {
      newPosX = wrapperWidth - innerWidth;
    }

    if (newPosX > 0) {
      newPosX = 0;
    }

    if (innerEle) {
      innerEle.style.transform = `translateX(${newPosX}px)`;
    }

    setPosX(newPosX);
  };

  return (
    <div className="pts:relative pts:w-full pts:max-w-4xl pts:overflow-hidden" hidden={!open}>
      {/* File Upload Container */}
      <div className="pts:p-2">
        <div className="pts:relative pts:h-[68px]">
          <button
            onClick={() => swipeList("left")}
            className="pts:top-[20px] pts:left-0 pts:z-10 pts:absolute pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:border-0 pts:rounded-full pts:w-6 pts:h-6 pts:transition-colors pts:cursor-pointer"
          >
            <svg className="pts:w-4 pts:h-4 pts:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          {/* File Items */}
          <div className="pts:overflow-hidden" ref={wrapperRef}>
            <div className="pts:whitespace-nowrap pts:transition-all pts:translate-x-0" ref={innerRef}>
              {files.map((file) => {
                const iconConfig = fileTypeIcons[file.type];

                return (
                  <div
                    key={file.id}
                    className="pts:inline-block pts:relative pts:bg-gray-50 pts:hover:bg-gray-100 pts:mr-2 pts:p-4 pts:rounded-lg pts:w-[196px] pts:h-[68px] pts:transition-colors pts:cursor-pointer"
                    onClick={() => (file.status === "failed" ? retryUpload(file.id) : undefined)}
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(file.id);
                      }}
                      className="pts:top-2 pts:right-2 pts:absolute pts:flex pts:justify-center pts:items-center pts:bg-gray-400 pts:hover:bg-gray-500 pts:border-0 pts:rounded-full pts:w-5 pts:h-5 pts:transition-colors pts:cursor-pointer"
                    >
                      <svg
                        className="pts:w-3 pts:h-3 pts:text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>

                    {/* File Icon and Info */}
                    <div className="pts:flex pts:items-center pts:gap-3 pts:mb-2">
                      <div
                        className={`pts:w-10 pts:h-10 ${iconConfig.bgColor} pts:rounded-lg pts:flex pts:items-center pts:justify-center`}
                      >
                        <span className={`pts:font-bold pts:text-sm ${iconConfig.textColor}`}>{iconConfig.icon}</span>
                      </div>
                      <div className="pts:flex-1 pts:min-w-0">
                        <div className="pts:font-medium pts:text-gray-900 pts:text-sm pts:truncate">{file.name}</div>
                        <div className="pts:flex pts:items-center pts:gap-2 pts:mt-1">
                          {file.status === "uploading" ? (
                            <span className="pts:font-medium pts:text-blue-500 pts:text-xs">
                              上传中 ... {Math.round(file.progress || 0)}%
                            </span>
                          ) : (
                            <span className="pts:text-gray-500 pts:text-xs">
                              {file.type === "markdown" ? "md" : file.size}
                            </span>
                          )}
                          {file.status === "failed" && (
                            <span className="pts:font-medium pts:text-red-500 pts:text-xs">上传失败</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <button
            onClick={() => swipeList("right")}
            className="pts:top-[20px] pts:right-0 pts:z-10 pts:absolute pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:border-0 pts:rounded-full pts:w-6 pts:h-6 pts:transition-colors pts:cursor-pointer"
          >
            <svg className="pts:w-4 pts:h-4 pts:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default FileList;
